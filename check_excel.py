#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel 파일 구조 확인 스크립트
"""

import pandas as pd
import numpy as np

def check_excel_structure(filename='a.xlsx'):
    """Excel 파일의 구조를 확인합니다."""
    
    try:
        # Excel 파일의 모든 시트 이름 확인
        xl_file = pd.ExcelFile(filename, engine='openpyxl')
        print(f"파일: {filename}")
        print(f"시트 목록: {xl_file.sheet_names}")
        
        # Sheet1 읽기
        df = pd.read_excel(filename, sheet_name='Sheet1', engine='openpyxl')
        
        print(f"\nSheet1 정보:")
        print(f"행 수: {len(df)}")
        print(f"열 수: {len(df.columns)}")
        print(f"열 이름: {list(df.columns)}")
        
        # 첫 10행 확인
        print(f"\n첫 10행 데이터:")
        print(df.head(10))
        
        # A열 (첫 번째 열) 데이터 타입 확인
        first_col = df.iloc[:, 0]
        print(f"\n첫 번째 열 정보:")
        print(f"열 이름: {df.columns[0] if len(df.columns) > 0 else 'N/A'}")
        print(f"데이터 타입: {first_col.dtype}")
        print(f"null이 아닌 값 개수: {first_col.notna().sum()}")
        print(f"null 값 개수: {first_col.isna().sum()}")
        
        # 실제 값이 있는 행들 확인
        non_null_values = first_col.dropna()
        if len(non_null_values) > 0:
            print(f"\n실제 값이 있는 첫 10개 행:")
            for i, value in enumerate(non_null_values.head(10)):
                print(f"  행 {non_null_values.index[i] + 1}: '{value}' (타입: {type(value)})")
        else:
            print("\n실제 값이 있는 행이 없습니다.")
            
    except Exception as e:
        print(f"오류 발생: {str(e)}")

if __name__ == "__main__":
    check_excel_structure()
